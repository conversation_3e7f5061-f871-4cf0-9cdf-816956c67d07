@echo off
chcp 65001 >nul
echo ========================================
echo Excel批量导入工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检查并安装依赖包...
pip install -r requirements_batch_import.txt

if errorlevel 1 (
    echo.
    echo 警告：依赖包安装可能有问题，但继续尝试运行...
    echo.
)

echo.
echo 选择操作：
echo 1. 创建Excel模板
echo 2. 执行批量导入
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo.
    echo 正在创建Excel模板...
    python create_excel_template.py
) else if "%choice%"=="2" (
    echo.
    echo 正在执行批量导入...
    python batch_import_example.py
) else if "%choice%"=="3" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选择
)

echo.
pause
