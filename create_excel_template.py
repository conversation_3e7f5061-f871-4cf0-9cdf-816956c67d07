#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel模板生成器
用于创建标准的Excel导入模板文件
"""

import pandas as pd
from datetime import datetime
import os

def create_excel_template(filename="项目数据模板.xlsx", include_sample_data=True):
    """
    创建Excel导入模板
    
    Args:
        filename: 输出文件名
        include_sample_data: 是否包含示例数据
    """
    
    # 定义列名和说明
    columns_info = {
        "bao_city": "城市（必填）",
        "gds": "供电所（必填）", 
        "gdlx": "工单类型（可选：增容/临时用电/新装）",
        "bao_bm": "报表编码（必填，纯数字）",
        "bao_name": "报表名称（必填）",
        "usercode": "用户代码（必填）",
        "htrl": "合同容量（可选，数字）",
        "bao_lx": "报表类型（必填）",
        "sj_ywsl": "实际业务受理时间（可选，格式：YYYY-MM-DD HH:MM:SS）",
        "sj_gdsj": "实际供电归档时间（可选，格式：YYYY-MM-DD HH:MM:SS）"
    }
    
    # 支持的城市列表
    supported_cities = ["城区", "环城", "汝南", "平舆", "新蔡", "确山", "泌阳", "正阳", "遂平", "西平", "上蔡"]
    
    # 支持的项目类型
    supported_project_types = ["高压用户", "低压用户", "光伏低压自然人", "光伏低压非自然人", "光伏高压", "华宇高压用户"]
    
    # 支持的工单类型
    supported_gdlx = ["增容", "临时用电", "新装"]
    
    if include_sample_data:
        # 创建示例数据
        sample_data = [
            {
                "bao_city": "城区",
                "gds": "城区供电所",
                "gdlx": "新装",
                "bao_bm": "202408010001",
                "bao_name": "示例项目1 - 高压用户新装",
                "usercode": "USER001",
                "htrl": 100.5,
                "bao_lx": "高压用户",
                "sj_ywsl": "2024-08-01 09:00:00",
                "sj_gdsj": "2024-08-01 17:00:00"
            },
            {
                "bao_city": "环城",
                "gds": "环城供电所",
                "gdlx": "增容",
                "bao_bm": "202408010002",
                "bao_name": "示例项目2 - 低压用户增容",
                "usercode": "USER002",
                "htrl": 200.0,
                "bao_lx": "低压用户",
                "sj_ywsl": "2024-08-01 10:00:00",
                "sj_gdsj": "2024-08-01 18:00:00"
            },
            {
                "bao_city": "汝南",
                "gds": "汝南供电所",
                "gdlx": "临时用电",
                "bao_bm": "202408010003",
                "bao_name": "示例项目3 - 光伏低压自然人",
                "usercode": "USER003",
                "htrl": 50.0,
                "bao_lx": "光伏低压自然人",
                "sj_ywsl": "2024-08-01 11:00:00",
                "sj_gdsj": "2024-08-01 19:00:00"
            }
        ]
        
        # 创建DataFrame
        df = pd.DataFrame(sample_data)
    else:
        # 创建空模板
        df = pd.DataFrame(columns=list(columns_info.keys()))
    
    # 使用ExcelWriter创建多个工作表
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 主数据表
        df.to_excel(writer, sheet_name='项目数据', index=False)
        
        # 说明表
        instructions_data = [
            ["列名", "说明", "是否必填", "数据类型", "示例"],
            ["bao_city", "城市", "是", "文本", "城区"],
            ["gds", "供电所", "是", "文本", "城区供电所"],
            ["gdlx", "工单类型", "否", "文本", "新装/增容/临时用电"],
            ["bao_bm", "报表编码", "是", "数字", "202408010001"],
            ["bao_name", "报表名称", "是", "文本", "测试项目1"],
            ["usercode", "用户代码", "是", "文本", "USER001"],
            ["htrl", "合同容量", "否", "数字", "100.5"],
            ["bao_lx", "报表类型", "是", "文本", "高压用户"],
            ["sj_ywsl", "实际业务受理时间", "否", "日期时间", "2024-08-01 09:00:00"],
            ["sj_gdsj", "实际供电归档时间", "否", "日期时间", "2024-08-01 17:00:00"]
        ]
        
        instructions_df = pd.DataFrame(instructions_data[1:], columns=instructions_data[0])
        instructions_df.to_excel(writer, sheet_name='字段说明', index=False)
        
        # 支持的城市列表
        cities_df = pd.DataFrame({"支持的城市": supported_cities})
        cities_df.to_excel(writer, sheet_name='城市列表', index=False)
        
        # 支持的项目类型列表
        project_types_df = pd.DataFrame({"支持的项目类型": supported_project_types})
        project_types_df.to_excel(writer, sheet_name='项目类型', index=False)
        
        # 支持的工单类型列表
        gdlx_df = pd.DataFrame({"支持的工单类型": supported_gdlx})
        gdlx_df.to_excel(writer, sheet_name='工单类型', index=False)
        
        # 注意事项
        notes_data = [
            ["注意事项"],
            [""],
            ["1. 数据格式要求："],
            ["   - 报表编码(bao_bm)必须是纯数字"],
            ["   - 合同容量(htrl)必须是数字，不能为负数"],
            ["   - 时间字段支持多种格式，建议使用：YYYY-MM-DD HH:MM:SS"],
            [""],
            ["2. 必填字段："],
            ["   - bao_city（城市）"],
            ["   - gds（供电所）"],
            ["   - bao_bm（报表编码）"],
            ["   - bao_name（报表名称）"],
            ["   - usercode（用户代码）"],
            ["   - bao_lx（报表类型）"],
            [""],
            ["3. 数据验证："],
            ["   - 城市必须在支持的城市列表中"],
            ["   - 项目类型必须在支持的项目类型列表中"],
            ["   - 工单类型必须在支持的工单类型列表中（如果填写）"],
            [""],
            ["4. 导入说明："],
            ["   - 请确保数据在'项目数据'工作表中"],
            ["   - 列名必须完全匹配，不能修改"],
            ["   - 可以删除示例数据，填入实际数据"],
            ["   - 重复的报表编码会被跳过或更新现有记录"],
            [""],
            ["5. 时间格式示例："],
            ["   - 标准格式：2024-08-01 09:00:00"],
            ["   - 日期格式：2024-08-01（自动补充时间为00:00:00）"],
            ["   - 其他格式：2024/08/01、08/01/2024等"],
            [""],
            ["6. 使用步骤："],
            ["   - 1. 在'项目数据'工作表中填入数据"],
            ["   - 2. 保存文件"],
            ["   - 3. 运行批量导入工具"],
            ["   - 4. 查看导入报告"]
        ]
        
        notes_df = pd.DataFrame(notes_data)
        notes_df.to_excel(writer, sheet_name='使用说明', index=False, header=False)
    
    print(f"✓ Excel模板已创建: {filename}")
    print(f"  包含工作表:")
    print(f"    - 项目数据: {'包含示例数据' if include_sample_data else '空模板'}")
    print(f"    - 字段说明: 详细的字段说明")
    print(f"    - 城市列表: 支持的城市")
    print(f"    - 项目类型: 支持的项目类型")
    print(f"    - 工单类型: 支持的工单类型")
    print(f"    - 使用说明: 详细的使用说明")
    
    return filename

def main():
    """
    主函数
    """
    print("Excel模板生成器")
    print("=" * 40)
    
    print("选择模板类型:")
    print("1. 包含示例数据的模板")
    print("2. 空白模板")
    print("3. 退出")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        filename = create_excel_template("项目数据模板_含示例.xlsx", include_sample_data=True)
        print(f"\n✓ 已创建包含示例数据的模板: {filename}")
        print("  您可以参考示例数据的格式，然后替换为实际数据")
        
    elif choice == "2":
        filename = create_excel_template("项目数据模板_空白.xlsx", include_sample_data=False)
        print(f"\n✓ 已创建空白模板: {filename}")
        print("  请在'项目数据'工作表中填入您的数据")
        
    elif choice == "3":
        print("退出程序")
        return
    else:
        print("无效选择")
        return
    
    print("\n使用提示:")
    print("1. 打开生成的Excel文件")
    print("2. 查看'使用说明'工作表了解详细要求")
    print("3. 在'项目数据'工作表中填入数据")
    print("4. 保存文件后使用批量导入工具")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
