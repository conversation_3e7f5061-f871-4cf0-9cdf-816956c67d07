# Excel批量导入工具 - 快速开始指南

## 🚀 快速开始（5分钟上手）

### 第一步：安装依赖
```bash
pip install pandas openpyxl xlrd requests python-dateutil
```

### 第二步：创建Excel模板
运行以下命令创建标准模板：
```bash
python create_excel_template.py
```
选择"1"创建包含示例数据的模板。

### 第三步：准备数据
1. 打开生成的Excel文件 `项目数据模板_含示例.xlsx`
2. 查看示例数据格式
3. 替换为您的实际数据
4. 保存文件为 `项目数据.xlsx`

### 第四步：配置登录信息
编辑 `batch_import_example.py` 文件，修改以下配置：
```python
SERVER_URL = "http://localhost:5000"  # 改为您的服务器地址
USERNAME = "your_phone_number"        # 改为您的手机号
PASSWORD = "your_password"            # 改为您的密码
EXCEL_FILE = "项目数据.xlsx"          # Excel文件路径
```

### 第五步：执行导入
```bash
python batch_import_example.py
```
选择"2"执行批量导入。

## 📋 Excel文件要求

您的Excel文件必须包含以下列：

| 列名 | 说明 | 必填 | 示例 |
|------|------|------|------|
| bao_city | 城市 | ✅ | 城区 |
| gds | 供电所 | ✅ | 城区供电所 |
| gdlx | 工单类型 | ❌ | 新装 |
| bao_bm | 报表编码 | ✅ | 202408010001 |
| bao_name | 报表名称 | ✅ | 测试项目1 |
| usercode | 用户代码 | ✅ | USER001 |
| htrl | 合同容量 | ❌ | 100.5 |
| bao_lx | 报表类型 | ✅ | 高压用户 |
| sj_ywsl | 业务受理时间 | ❌ | 2024-08-01 09:00:00 |
| sj_gdsj | 项目归档时间 | ❌ | 2024-08-01 17:00:00 |

## 🎯 支持的数据值

### 城市 (bao_city)
城区、环城、汝南、平舆、新蔡、确山、泌阳、正阳、遂平、西平、上蔡

### 项目类型 (bao_lx)
高压用户、低压用户、光伏低压自然人、光伏低压非自然人、光伏高压、华宇高压用户

### 工单类型 (gdlx)
增容、临时用电、新装

## 🛠️ Windows用户快捷方式

双击运行 `运行批量导入.bat` 文件，按提示操作即可。

## ⚠️ 常见问题

### 1. 登录失败
- 检查用户名（手机号）和密码
- 确认服务器地址是否正确

### 2. Excel读取失败
- 确保文件格式为 .xlsx 或 .xls
- 检查列名是否完全匹配（区分大小写）

### 3. 数据验证失败
- 必填字段不能为空
- 报表编码必须是纯数字
- 城市和项目类型必须在支持列表中

### 4. 导入部分失败
- 查看生成的导入报告文件
- 根据错误信息修正数据后重新导入

## 📊 导入结果

导入完成后会显示：
- 总计处理条数
- 成功导入条数
- 失败条数
- 跳过条数

详细报告会保存为 `import_report_YYYYMMDD_HHMMSS.txt` 文件。

## 🔧 高级配置

如需自定义配置，可以直接使用 `excel_batch_import.py`：

```python
from excel_batch_import import ExcelBatchImporter

# 创建导入工具
importer = ExcelBatchImporter("http://your-server:5000")

# 登录
importer.login("phone", "password")

# 批量导入
results = importer.batch_import(
    file_path="data.xlsx",
    max_retries=3,              # 重试次数
    delay_between_requests=0.1  # 请求间隔
)

# 保存报告
importer.save_import_report(results)
```

## 📞 技术支持

如遇问题，请检查：
1. 导入报告文件中的详细错误信息
2. Excel文件格式和数据是否正确
3. 网络连接和服务器状态
4. 用户权限是否足够

---

**提示**：建议先用少量数据测试，确认无误后再进行大批量导入。
