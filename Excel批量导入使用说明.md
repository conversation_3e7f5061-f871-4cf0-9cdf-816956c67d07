# Excel批量导入工具使用说明

## 功能概述

本工具可以读取Excel文件中的项目数据，并通过调用现有的"/addbao"接口批量创建项目。支持数据验证、错误处理、重试机制和详细的操作结果反馈。

## 安装依赖

首先安装必要的Python包：

```bash
pip install -r requirements_batch_import.txt
```

或者手动安装：

```bash
pip install pandas openpyxl xlrd requests python-dateutil
```

## Excel文件格式要求

Excel文件必须包含以下列（列名必须完全匹配）：

| 列名 | 说明 | 类型 | 是否必填 | 示例 |
|------|------|------|----------|------|
| bao_city | 城市 | 文本 | 是 | 城区 |
| gds | 供电所 | 文本 | 是 | 城区供电所 |
| gdlx | 供电类型 | 文本 | 否 | 新装/增容/临时用电 |
| bao_bm | 报表编码 | 数字 | 是 | 202408010001 |
| bao_name | 报表名称 | 文本 | 是 | 测试项目1 |
| usercode | 用户代码 | 文本 | 是 | USER001 |
| htrl | 合同容量 | 数字 | 否 | 100.5 |
| bao_lx | 报表类型 | 文本 | 是 | 高压用户 |
| sj_ywsl | 实际业务数量 | 日期时间 | 否 | 2024-08-01 09:00:00 |
| sj_gdsj | 实际供电时间 | 日期时间 | 否 | 2024-08-01 17:00:00 |

### 数据格式说明

1. **城市字段 (bao_city)**：必须是系统支持的城市名称
   - 支持的城市：城区、环城、汝南、平舆、新蔡、确山、泌阳、正阳、遂平、西平、上蔡

2. **报表编码 (bao_bm)**：必须是纯数字

3. **工单类型 (gdlx)**：可选值为 "增容"、"临时用电"、"新装"

4. **报表类型 (bao_lx)**：必须是系统支持的项目类型
   - 支持的类型：高压用户、低压用户、光伏低压自然人、光伏低压非自然人、光伏高压、华宇高压用户

5. **合同容量 (htrl)**：数字类型，不能为负数

6. **时间字段 (sj_ywsl, sj_gdsj)**：支持多种日期格式
   - 标准格式：2024-08-01 09:00:00
   - 日期格式：2024-08-01（自动补充00:00:00）
   - 其他格式：2024/08/01、08/01/2024等

## 使用方法

### 方法1：使用简化版本

1. 修改 `batch_import_example.py` 中的配置参数：
   ```python
   SERVER_URL = "http://your-server:5000"  # 修改为您的服务器地址
   USERNAME = "your_phone_number"          # 修改为您的用户名
   PASSWORD = "your_password"              # 修改为您的密码
   EXCEL_FILE = "项目数据.xlsx"            # 修改为您的Excel文件路径
   ```

2. 运行脚本：
   ```bash
   python batch_import_example.py
   ```

3. 选择操作：
   - 选择1：创建示例Excel文件
   - 选择2：执行批量导入

### 方法2：使用完整版本

1. 运行主程序：
   ```bash
   python excel_batch_import.py
   ```

2. 按提示输入用户名和密码

3. 如果需要验证码，程序会自动保存验证码图片为 `captcha.jpg`，查看后输入验证码

4. 确保Excel文件存在并命名为 `项目数据.xlsx`

### 方法3：编程方式使用

```python
from excel_batch_import import ExcelBatchImporter

# 创建导入工具
importer = ExcelBatchImporter("http://localhost:5000")

# 登录
if importer.login("your_phone", "your_password"):
    # 执行批量导入
    results = importer.batch_import("项目数据.xlsx")
    
    # 保存报告
    importer.save_import_report(results)
```

## 功能特性

### 1. 数据验证
- 必填字段检查
- 数据格式验证
- 业务规则验证

### 2. 错误处理
- 自动重试机制（默认3次）
- 详细的错误信息记录
- 跳过无效数据继续处理

### 3. 进度反馈
- 实时显示处理进度
- 成功/失败/跳过统计
- 详细的操作日志

### 4. 结果报告
- 自动生成导入报告文件
- 包含成功、失败、跳过的详细记录
- 便于后续问题排查

## 配置参数

可以调整以下参数来优化导入性能：

```python
results = importer.batch_import(
    file_path="项目数据.xlsx",
    max_retries=3,              # 最大重试次数
    delay_between_requests=0.1  # 请求间隔时间（秒）
)
```

- `max_retries`：失败时的最大重试次数
- `delay_between_requests`：请求之间的延迟时间，避免服务器压力过大

## 常见问题

### 1. 登录失败
- 检查用户名和密码是否正确
- 确认服务器地址是否正确
- 如果需要验证码，确保正确输入

### 2. Excel文件读取失败
- 确认文件格式为.xlsx或.xls
- 检查文件是否被其他程序占用
- 确认列名是否完全匹配要求

### 3. 数据验证失败
- 检查必填字段是否为空
- 确认数据格式是否符合要求
- 查看详细错误信息进行修正

### 4. API调用失败
- 检查网络连接
- 确认服务器是否正常运行
- 查看具体错误信息

## 输出文件

程序运行后会生成以下文件：

1. `import_report_YYYYMMDD_HHMMSS.txt`：详细的导入报告
2. `captcha.jpg`：验证码图片（如果需要）

## 注意事项

1. **数据备份**：导入前请备份重要数据
2. **权限检查**：确保登录用户有创建项目的权限
3. **服务器负载**：大量数据导入时注意服务器负载，可适当增加请求间隔
4. **重复数据**：系统会检查报表编码是否重复，重复的记录会被跳过或更新
5. **时间格式**：时间字段支持多种格式，但建议使用标准格式 YYYY-MM-DD HH:MM:SS

## 技术支持

如遇到问题，请检查：
1. 错误日志和导入报告
2. Excel文件格式和数据
3. 网络连接和服务器状态
4. 用户权限和登录状态
