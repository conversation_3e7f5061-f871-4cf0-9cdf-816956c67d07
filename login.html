<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            background-color: #f0f8ff;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-image: url(./bj.jpg);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(240, 248, 255, 0.7);
            backdrop-filter: blur(5px);
        }

        .login-container {
            background-color: rgba(255, 255, 255, 0.9);
            padding: 35px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 
                        0 0 20px rgba(0, 123, 255, 0.2);
            width: 320px;
            position: relative;
            border: 1px solid rgba(0, 123, 255, 0.2);
            backdrop-filter: blur(5px);
            z-index: 1;
        }

        h3 {
            text-align: center;
            margin-bottom: 25px;
            color: #0056b3;
            font-weight: 600;
            letter-spacing: 1px;
            position: relative;
        }
        
        h3::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, #007bff, #00c6ff);
            border-radius: 3px;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(0, 123, 255, 0.3);
            border-radius: 6px;
            box-sizing: border-box;
            background-color: rgba(255, 255, 255, 0.8);
            color: #333;
            transition: all 0.3s ease;
            outline: none;
        }
        
        input[type="text"]:focus,
        input[type="password"]:focus {
            border-color: #007bff;
            box-shadow: 0 0 8px rgba(0, 123, 255, 0.4);
        }

        button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        button:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        
        button:active {
            transform: translateY(1px);
        }

        .error-message {
            color: #dc3545;
            margin-top: 15px;
            text-align: center;
            font-size: 14px;
        }
        
        .captcha-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .captcha-input {
            flex: 1;
        }
        
        .captcha-image {
            height: 44px;
            border-radius: 6px;
            cursor: pointer;
            border: 1px solid rgba(0, 123, 255, 0.3);
        }

        /* 密码修改弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: rgba(255, 255, 255, 0.95);
            margin: 5% auto;
            padding: 30px;
            border-radius: 12px;
            width: 400px;
            max-width: 90%;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(0, 123, 255, 0.2);
            position: relative;
        }

        .modal h3 {
            color: #dc3545;
            margin-bottom: 20px;
            text-align: center;
        }

        .modal h3::after {
            background: linear-gradient(90deg, #dc3545, #ff6b6b);
        }

        .password-requirement {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            font-size: 14px;
            color: #495057;
        }

        .password-requirement ul {
            margin: 0;
            padding-left: 20px;
        }

        .password-requirement li {
            margin-bottom: 5px;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .modal-buttons button {
            flex: 1;
        }

        .btn-cancel {
            background: linear-gradient(135deg, #6c757d, #495057) !important;
        }

        .btn-cancel:hover {
            background: linear-gradient(135deg, #495057, #6c757d) !important;
        }

        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }

        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }
    </style>
</head>

<body>

    <div class="login-container">
        <h3>华宇收资管理系统-用户登录</h3>
        <form id="loginForm" action="http://127.0.0.1:5000/login" method="post">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <div class="form-group">
                <label for="captcha">验证码:</label>
                <div class="captcha-container">
                    <input type="text" id="captcha" name="captcha" class="captcha-input" required>
                    <img id="captchaImage" class="captcha-image" alt="验证码" title="点击刷新验证码">
                </div>
            </div>
            <button type="submit">登录</button>
        </form>
        <div id="errorMessage" class="error-message"></div>
    </div>

    <!-- 密码修改弹窗 -->
    <div id="passwordModal" class="modal">
        <div class="modal-content">
            <h3>强制密码修改</h3>
            <div class="password-requirement">
                <strong>安全提示：</strong>
                <ul>
                    <li>检测到您使用的是默认密码，为了账户安全，请立即修改密码</li>
                    <li>新密码长度至少6位</li>
                    <li>必须包含字母和数字</li>
                    <li>不能使用默认密码 "123456"</li>
                </ul>
            </div>

            <form id="passwordChangeForm">
                <div class="form-group">
                    <label for="currentPassword">当前密码:</label>
                    <input type="password" id="currentPassword" name="currentPassword" required>
                </div>
                <div class="form-group">
                    <label for="newPassword">新密码:</label>
                    <input type="password" id="newPassword" name="newPassword" required>
                    <div id="passwordStrength" class="password-strength"></div>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">确认新密码:</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                </div>
                <div class="modal-buttons">
                    <button type="button" class="btn-cancel" onclick="logout()">取消登录</button>
                    <button type="submit">修改密码</button>
                </div>
            </form>
            <div id="passwordErrorMessage" class="error-message"></div>
        </div>
    </div>

    <script>
        // 存储验证码ID
        let captchaId = '';
        
        // 页面加载时获取验证码
        document.addEventListener('DOMContentLoaded', function() {
            refreshCaptcha();
        });
        
        // 点击验证码图片刷新验证码
        document.getElementById('captchaImage').addEventListener('click', function() {
            refreshCaptcha();
        });
        
        // 刷新验证码的函数
        function refreshCaptcha() {
            fetch('http://127.0.0.1:5000/captcha?' + new Date().getTime())
                .then(response => {
                    // 保存验证码ID
                    captchaId = response.headers.get('X-Captcha-ID');
                    return response.blob();
                })
                .then(blob => {
                    // 显示验证码图片
                    document.getElementById('captchaImage').src = URL.createObjectURL(blob);
                })
                .catch(error => {
                    console.error('获取验证码失败:', error);
                });
        }

        document.getElementById('loginForm').addEventListener('submit', function (event) {
            // 这里可以添加前端验证逻辑
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const captcha = document.getElementById('captcha').value;

            if (!username || !password) {
                document.getElementById('errorMessage').textContent = '请输入用户名和密码';
                event.preventDefault();
                return;
            }
            
            if (!captcha) {
                document.getElementById('errorMessage').textContent = '请输入验证码';
                event.preventDefault();
                return;
            }

            // 使用AJAX提交
            event.preventDefault();
            fetch('http://127.0.0.1:5000/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Captcha-ID': captchaId // 添加验证码ID
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    captcha: captcha
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.access_token) {
                        // 登录成功处理
                        localStorage.setItem('access_token', data.access_token);
                        localStorage.setItem('city', data.city);
                        localStorage.setItem('username', data.username);

                        // 检查是否需要强制修改密码
                        if (data.need_password_change) {
                            showPasswordChangeModal();
                        } else {
                            window.location.href = './3.html';
                        }
                    } else {
                        document.getElementById('errorMessage').textContent = data.msg || '登录失败';
                        refreshCaptcha(); // 登录失败时刷新验证码
                    }
                })
                .catch(error => {
                    document.getElementById('errorMessage').textContent = '网络错误，请重试';
                    refreshCaptcha(); // 出错时刷新验证码
                });

        });

        // 显示密码修改弹窗
        function showPasswordChangeModal() {
            document.getElementById('passwordModal').style.display = 'block';
            // 禁用页面滚动
            document.body.style.overflow = 'hidden';
        }

        // 隐藏密码修改弹窗
        function hidePasswordChangeModal() {
            document.getElementById('passwordModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('city');
            localStorage.removeItem('username');
            hidePasswordChangeModal();
            location.reload();
        }

        // 检查密码强度
        function checkPasswordStrength(password) {
            const strengthElement = document.getElementById('passwordStrength');
            let strength = 0;
            let message = '';

            if (password.length >= 6) strength++;
            if (/[a-zA-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^a-zA-Z0-9]/.test(password)) strength++;

            if (password === '123456') {
                message = '不能使用默认密码';
                strengthElement.className = 'password-strength strength-weak';
            } else if (strength < 2) {
                message = '密码强度：弱';
                strengthElement.className = 'password-strength strength-weak';
            } else if (strength < 3) {
                message = '密码强度：中等';
                strengthElement.className = 'password-strength strength-medium';
            } else {
                message = '密码强度：强';
                strengthElement.className = 'password-strength strength-strong';
            }

            strengthElement.textContent = message;
        }

        // 新密码输入时检查强度
        document.addEventListener('DOMContentLoaded', function() {
            const newPasswordInput = document.getElementById('newPassword');
            if (newPasswordInput) {
                newPasswordInput.addEventListener('input', function() {
                    checkPasswordStrength(this.value);
                });
            }
        });

        // 密码修改表单提交
        document.addEventListener('DOMContentLoaded', function() {
            const passwordForm = document.getElementById('passwordChangeForm');
            if (passwordForm) {
                passwordForm.addEventListener('submit', function(event) {
                    event.preventDefault();

                    const currentPassword = document.getElementById('currentPassword').value;
                    const newPassword = document.getElementById('newPassword').value;
                    const confirmPassword = document.getElementById('confirmPassword').value;
                    const errorElement = document.getElementById('passwordErrorMessage');

                    // 前端验证
                    if (!currentPassword || !newPassword || !confirmPassword) {
                        errorElement.textContent = '所有字段都是必填的';
                        return;
                    }

                    if (newPassword !== confirmPassword) {
                        errorElement.textContent = '新密码和确认密码不一致';
                        return;
                    }

                    if (newPassword === '123456') {
                        errorElement.textContent = '新密码不能是默认密码123456';
                        return;
                    }

                    if (newPassword.length < 6) {
                        errorElement.textContent = '密码长度至少6位';
                        return;
                    }

                    const hasLetter = /[a-zA-Z]/.test(newPassword);
                    const hasDigit = /[0-9]/.test(newPassword);
                    if (!hasLetter || !hasDigit) {
                        errorElement.textContent = '密码必须包含字母和数字';
                        return;
                    }

                    // 提交密码修改请求
                    const token = localStorage.getItem('access_token');
                    fetch('http://127.0.0.1:5000/change_password', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + token
                        },
                        body: JSON.stringify({
                            current_password: currentPassword,
                            new_password: newPassword,
                            confirm_password: confirmPassword
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.msg === '密码修改成功') {
                            alert('密码修改成功！');
                            hidePasswordChangeModal();
                            window.location.href = './3.html';
                        } else {
                            errorElement.textContent = data.msg || '密码修改失败';
                        }
                    })
                    .catch(error => {
                        errorElement.textContent = '网络错误，请重试';
                        console.error('密码修改失败:', error);
                    });
                });
            }
        });
    </script>
</body>

</html>